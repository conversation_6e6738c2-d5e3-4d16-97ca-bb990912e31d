/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.tmdb

import com.google.jetstream.data.tmdb.api.TmdbApiService
import com.google.jetstream.data.tmdb.models.TmdbMovie
import com.google.jetstream.data.tmdb.models.TmdbMovieDetails
import com.google.jetstream.data.tmdb.models.TmdbTvShow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TMDB服务类
 */
@Singleton
class TmdbService @Inject constructor(
    private val apiService: TmdbApiService
) {
    
    private val authHeader = "Bearer ${TmdbConfig.ACCESS_TOKEN}"
    
    /**
     * 搜索电影
     */
    suspend fun searchMovies(query: String): Result<List<TmdbMovie>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.searchMovies(
                authorization = authHeader,
                query = query
            )
            
            if (response.isSuccessful) {
                val searchResponse = response.body()
                if (searchResponse != null) {
                    Result.success(searchResponse.results)
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                Result.failure(Exception("搜索失败: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 搜索电视剧
     */
    suspend fun searchTvShows(query: String): Result<List<TmdbTvShow>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.searchTvShows(
                authorization = authHeader,
                query = query
            )
            
            if (response.isSuccessful) {
                val searchResponse = response.body()
                if (searchResponse != null) {
                    Result.success(searchResponse.results)
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                Result.failure(Exception("搜索失败: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取电影详情
     */
    suspend fun getMovieDetails(movieId: Int): Result<TmdbMovieDetails> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getMovieDetails(
                authorization = authHeader,
                movieId = movieId
            )
            
            if (response.isSuccessful) {
                val movieDetails = response.body()
                if (movieDetails != null) {
                    Result.success(movieDetails)
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                Result.failure(Exception("获取电影详情失败: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 多媒体搜索（同时搜索电影和电视剧）
     */
    suspend fun searchMulti(query: String): Result<List<TmdbMovie>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.searchMulti(
                authorization = authHeader,
                query = query
            )
            
            if (response.isSuccessful) {
                val searchResponse = response.body()
                if (searchResponse != null) {
                    Result.success(searchResponse.results)
                } else {
                    Result.failure(Exception("响应体为空"))
                }
            } else {
                Result.failure(Exception("多媒体搜索失败: ${response.code()} ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
