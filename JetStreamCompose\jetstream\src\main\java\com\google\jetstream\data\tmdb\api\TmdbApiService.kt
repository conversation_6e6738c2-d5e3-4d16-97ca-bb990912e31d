/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.tmdb.api

import com.google.jetstream.data.tmdb.models.TmdbMovieDetails
import com.google.jetstream.data.tmdb.models.TmdbMovieSearchResponse
import com.google.jetstream.data.tmdb.models.TmdbTvSearchResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * TMDB API接口
 */
interface TmdbApiService {
    
    /**
     * 搜索电影
     */
    @GET("search/movie")
    suspend fun searchMovies(
        @Header("Authorization") authorization: String,
        @Query("query") query: String,
        @Query("language") language: String = "zh-CN",
        @Query("page") page: Int = 1,
        @Query("include_adult") includeAdult: Boolean = false
    ): Response<TmdbMovieSearchResponse>
    
    /**
     * 搜索电视剧
     */
    @GET("search/tv")
    suspend fun searchTvShows(
        @Header("Authorization") authorization: String,
        @Query("query") query: String,
        @Query("language") language: String = "zh-CN",
        @Query("page") page: Int = 1,
        @Query("include_adult") includeAdult: Boolean = false
    ): Response<TmdbTvSearchResponse>
    
    /**
     * 获取电影详情
     */
    @GET("movie/{movie_id}")
    suspend fun getMovieDetails(
        @Header("Authorization") authorization: String,
        @Path("movie_id") movieId: Int,
        @Query("language") language: String = "zh-CN"
    ): Response<TmdbMovieDetails>
    
    /**
     * 多媒体搜索（同时搜索电影和电视剧）
     */
    @GET("search/multi")
    suspend fun searchMulti(
        @Header("Authorization") authorization: String,
        @Query("query") query: String,
        @Query("language") language: String = "zh-CN",
        @Query("page") page: Int = 1,
        @Query("include_adult") includeAdult: Boolean = false
    ): Response<TmdbMovieSearchResponse>
}
