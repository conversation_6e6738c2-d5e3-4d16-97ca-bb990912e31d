/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.scraper

import com.google.jetstream.data.entities.Movie
import com.google.jetstream.data.repositories.ScrapedMediaRepository
import com.google.jetstream.data.repositories.WebDavRepository
import com.google.jetstream.data.tmdb.TmdbConfig
import com.google.jetstream.data.tmdb.TmdbService
import com.google.jetstream.data.tmdb.models.TmdbMovie
import com.google.jetstream.data.tmdb.models.TmdbTvShow
import com.google.jetstream.data.webdav.WebDavService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 媒体刮削服务
 */
@Singleton
class MediaScrapingService @Inject constructor(
    private val webDavService: WebDavService,
    private val tmdbService: TmdbService,
    private val webDavRepository: WebDavRepository,
    private val scrapedMediaRepository: ScrapedMediaRepository
) {
    
    /**
     * 刮削进度状态
     */
    data class ScrapingProgress(
        val currentFile: String = "",
        val processedFiles: Int = 0,
        val totalFiles: Int = 0,
        val foundMovies: Int = 0,
        val foundTvShows: Int = 0,
        val isCompleted: Boolean = false,
        val error: String? = null
    )
    
    /**
     * 刮削结果
     */
    data class ScrapingResult(
        val movies: List<Movie>,
        val tvShows: List<Movie>,
        val errors: List<String>
    )
    
    /**
     * 开始刮削所有配置的资源目录
     */
    suspend fun startScraping(): Flow<ScrapingProgress> = flow {
        try {
            emit(ScrapingProgress(currentFile = "正在获取资源目录配置..."))
            
            // 获取所有资源目录
            val resourceDirectories = webDavRepository.getAllResourceDirectories()
            var allDirectories: List<com.google.jetstream.data.database.entities.ResourceDirectoryEntity> = emptyList()
            
            resourceDirectories.collect { directories ->
                allDirectories = directories
            }
            
            if (allDirectories.isEmpty()) {
                emit(ScrapingProgress(error = "未找到配置的资源目录"))
                return@flow
            }
            
            val allFiles = mutableListOf<Pair<String, String>>() // (filePath, webDavConfigId)
            
            // 收集所有文件
            for (directory in allDirectories) {
                emit(ScrapingProgress(currentFile = "正在扫描目录: ${directory.name}"))
                
                // 获取WebDAV配置
                val webDavConfig = webDavRepository.getWebDavConfigEntityById(directory.webDavConfigId)
                if (webDavConfig != null) {
                    // 设置WebDAV配置
                    val config = com.google.jetstream.data.webdav.WebDavConfig(
                        serverUrl = webDavConfig.serverUrl,
                        username = webDavConfig.username,
                        password = webDavConfig.password,
                        isEnabled = true
                    )
                    webDavService.setConfig(config)
                    
                    // 递归扫描目录
                    val files = scanDirectoryRecursively(directory.path)
                    allFiles.addAll(files.map { it to directory.webDavConfigId })
                }
            }
            
            // 过滤视频文件
            val videoFiles = allFiles.filter { (filePath, _) ->
                FileNameParser.isVideoFile(filePath.substringAfterLast('/'))
            }
            
            emit(ScrapingProgress(
                currentFile = "找到 ${videoFiles.size} 个视频文件，开始刮削...",
                totalFiles = videoFiles.size
            ))
            
            val movies = mutableListOf<Movie>()
            val tvShows = mutableListOf<Movie>()
            val errors = mutableListOf<String>()
            
            // 处理每个视频文件
            videoFiles.forEachIndexed { index, (filePath, _) ->
                val fileName = filePath.substringAfterLast('/')
                emit(ScrapingProgress(
                    currentFile = fileName,
                    processedFiles = index,
                    totalFiles = videoFiles.size,
                    foundMovies = movies.size,
                    foundTvShows = tvShows.size
                ))
                
                try {
                    // 检查文件是否已经刮削过
                    if (scrapedMediaRepository.isMediaExistsByFilePath(filePath)) {
                        // 文件已存在，跳过
                        continue
                    }

                    val parsedInfo = FileNameParser.parseFileName(fileName)

                    if (parsedInfo.isMovie) {
                        // 搜索电影
                        val movieResult = tmdbService.searchMovies(parsedInfo.cleanName)
                        movieResult.getOrNull()?.firstOrNull()?.let { tmdbMovie ->
                            // 保存到数据库
                            scrapedMediaRepository.saveTmdbMovie(tmdbMovie, filePath, fileName)
                            movies.add(convertTmdbMovieToMovie(tmdbMovie, filePath))
                        }
                    } else {
                        // 搜索电视剧
                        val tvResult = tmdbService.searchTvShows(parsedInfo.cleanName)
                        tvResult.getOrNull()?.firstOrNull()?.let { tmdbTvShow ->
                            // 保存到数据库
                            scrapedMediaRepository.saveTmdbTvShow(tmdbTvShow, filePath, fileName)
                            tvShows.add(convertTmdbTvShowToMovie(tmdbTvShow, filePath))
                        }
                    }
                } catch (e: Exception) {
                    errors.add("处理文件 $fileName 时出错: ${e.message}")
                }
            }
            
            emit(ScrapingProgress(
                currentFile = "刮削完成",
                processedFiles = videoFiles.size,
                totalFiles = videoFiles.size,
                foundMovies = movies.size,
                foundTvShows = tvShows.size,
                isCompleted = true
            ))
            
        } catch (e: Exception) {
            emit(ScrapingProgress(error = "刮削过程中出错: ${e.message}"))
        }
    }
    
    /**
     * 递归扫描目录
     */
    private suspend fun scanDirectoryRecursively(path: String): List<String> = withContext(Dispatchers.IO) {
        val allFiles = mutableListOf<String>()
        
        try {
            val result = webDavService.listDirectory(path)
            if (result is com.google.jetstream.data.webdav.WebDavResult.Success) {
                for (item in result.data) {
                    if (item.isDirectory) {
                        // 递归扫描子目录
                        val subFiles = scanDirectoryRecursively(item.path)
                        allFiles.addAll(subFiles)
                    } else {
                        // 添加文件
                        allFiles.add(item.path)
                    }
                }
            }
        } catch (e: Exception) {
            // 忽略错误，继续处理其他目录
        }
        
        allFiles
    }
    
    /**
     * 将TMDB电影转换为应用的Movie对象
     */
    private fun convertTmdbMovieToMovie(tmdbMovie: TmdbMovie, filePath: String): Movie {
        return Movie(
            id = UUID.randomUUID().toString(),
            videoUri = filePath,
            subtitleUri = null,
            posterUri = TmdbConfig.getPosterUrl(tmdbMovie.posterPath),
            name = tmdbMovie.title,
            description = tmdbMovie.overview ?: ""
        )
    }
    
    /**
     * 将TMDB电视剧转换为应用的Movie对象
     */
    private fun convertTmdbTvShowToMovie(tmdbTvShow: TmdbTvShow, filePath: String): Movie {
        return Movie(
            id = UUID.randomUUID().toString(),
            videoUri = filePath,
            subtitleUri = null,
            posterUri = TmdbConfig.getPosterUrl(tmdbTvShow.posterPath),
            name = tmdbTvShow.name,
            description = tmdbTvShow.overview ?: ""
        )
    }
}
