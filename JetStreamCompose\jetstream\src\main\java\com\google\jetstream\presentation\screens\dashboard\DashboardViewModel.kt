/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.presentation.screens.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.jetstream.data.scraper.MediaScrapingService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Dashboard ViewModel
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val mediaScrapingService: MediaScrapingService
) : ViewModel() {
    
    private val _scrapingProgress = MutableStateFlow<MediaScrapingService.ScrapingProgress?>(null)
    val scrapingProgress: StateFlow<MediaScrapingService.ScrapingProgress?> = _scrapingProgress.asStateFlow()
    
    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()
    
    /**
     * 开始刷新媒体库
     */
    fun startRefresh() {
        if (_isRefreshing.value) {
            return // 已经在刷新中，避免重复操作
        }
        
        viewModelScope.launch {
            _isRefreshing.value = true
            _scrapingProgress.value = null
            
            try {
                mediaScrapingService.startScraping().collect { progress ->
                    _scrapingProgress.value = progress
                    if (progress.isCompleted || progress.error != null) {
                        _isRefreshing.value = false
                    }
                }
            } catch (e: Exception) {
                _scrapingProgress.value = MediaScrapingService.ScrapingProgress(
                    error = "刷新失败: ${e.message}"
                )
                _isRefreshing.value = false
            }
        }
    }
    
    /**
     * 清除刮削进度状态
     */
    fun clearScrapingProgress() {
        _scrapingProgress.value = null
    }
}
