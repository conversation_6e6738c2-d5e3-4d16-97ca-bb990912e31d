/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.presentation.screens.dashboard

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.tv.material3.CircularProgressIndicator
import androidx.tv.material3.Icon
import androidx.tv.material3.LinearProgressIndicator
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.Text
import com.google.jetstream.data.scraper.MediaScrapingService
import kotlinx.coroutines.delay

@Composable
fun RefreshProgressDialog(
    progress: MediaScrapingService.ScrapingProgress?,
    onDismiss: () -> Unit
) {
    if (progress != null) {
        Dialog(onDismissRequest = onDismiss) {
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .background(MaterialTheme.colorScheme.surface)
                    .padding(24.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 标题
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        when {
                            progress.error != null -> {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(24.dp)
                                )
                                Text(
                                    text = "刷新失败",
                                    style = MaterialTheme.typography.headlineSmall,
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            progress.isCompleted -> {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = null,
                                    tint = Color.Green,
                                    modifier = Modifier.size(24.dp)
                                )
                                Text(
                                    text = "刷新完成",
                                    style = MaterialTheme.typography.headlineSmall,
                                    color = Color.Green
                                )
                            }
                            else -> {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                                Text(
                                    text = "正在刷新媒体库",
                                    style = MaterialTheme.typography.headlineSmall
                                )
                            }
                        }
                    }
                    
                    // 当前文件信息
                    if (progress.currentFile.isNotEmpty()) {
                        Text(
                            text = progress.currentFile,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    // 进度条
                    if (progress.totalFiles > 0 && !progress.isCompleted && progress.error == null) {
                        Column {
                            LinearProgressIndicator(
                                progress = { progress.processedFiles.toFloat() / progress.totalFiles.toFloat() },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(8.dp),
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "${progress.processedFiles} / ${progress.totalFiles} 文件",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    
                    // 结果统计
                    if (progress.foundMovies > 0 || progress.foundTvShows > 0) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "电影: ${progress.foundMovies}",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = "电视剧: ${progress.foundTvShows}",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                    
                    // 错误信息
                    if (progress.error != null) {
                        Text(
                            text = progress.error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
        
        // 自动关闭对话框
        if (progress.isCompleted || progress.error != null) {
            LaunchedEffect(progress) {
                delay(3000) // 3秒后自动关闭
                onDismiss()
            }
        }
    }
}
