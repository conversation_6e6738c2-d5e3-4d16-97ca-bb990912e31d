/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.google.jetstream.data.database.entities.ScrapedMediaEntity
import kotlinx.coroutines.flow.Flow

/**
 * 刮削媒体数据访问对象
 */
@Dao
interface ScrapedMediaDao {
    
    /**
     * 获取所有刮削的媒体
     */
    @Query("SELECT * FROM scraped_media ORDER BY addedAt DESC")
    fun getAllScrapedMedia(): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取所有电影
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'movie' ORDER BY addedAt DESC")
    fun getAllMovies(): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取所有电视剧
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'tv' ORDER BY addedAt DESC")
    fun getAllTvShows(): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 根据ID获取媒体
     */
    @Query("SELECT * FROM scraped_media WHERE id = :id")
    suspend fun getMediaById(id: String): ScrapedMediaEntity?
    
    /**
     * 根据TMDB ID获取媒体
     */
    @Query("SELECT * FROM scraped_media WHERE tmdbId = :tmdbId AND mediaType = :mediaType")
    suspend fun getMediaByTmdbId(tmdbId: Int, mediaType: String): ScrapedMediaEntity?
    
    /**
     * 根据文件路径获取媒体
     */
    @Query("SELECT * FROM scraped_media WHERE filePath = :filePath")
    suspend fun getMediaByFilePath(filePath: String): ScrapedMediaEntity?
    
    /**
     * 搜索媒体
     */
    @Query("SELECT * FROM scraped_media WHERE title LIKE '%' || :query || '%' OR originalTitle LIKE '%' || :query || '%' ORDER BY addedAt DESC")
    fun searchMedia(query: String): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取最近添加的电影
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'movie' ORDER BY addedAt DESC LIMIT :limit")
    fun getRecentMovies(limit: Int = 10): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取最近添加的电视剧
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'tv' ORDER BY addedAt DESC LIMIT :limit")
    fun getRecentTvShows(limit: Int = 10): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取热门电影（按评分排序）
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'movie' AND voteAverage > 0 ORDER BY voteAverage DESC, voteCount DESC LIMIT :limit")
    fun getTopRatedMovies(limit: Int = 10): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 获取热门电视剧（按评分排序）
     */
    @Query("SELECT * FROM scraped_media WHERE mediaType = 'tv' AND voteAverage > 0 ORDER BY voteAverage DESC, voteCount DESC LIMIT :limit")
    fun getTopRatedTvShows(limit: Int = 10): Flow<List<ScrapedMediaEntity>>
    
    /**
     * 插入媒体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMedia(media: ScrapedMediaEntity)
    
    /**
     * 批量插入媒体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaList(mediaList: List<ScrapedMediaEntity>)
    
    /**
     * 更新媒体
     */
    @Update
    suspend fun updateMedia(media: ScrapedMediaEntity)
    
    /**
     * 删除媒体
     */
    @Delete
    suspend fun deleteMedia(media: ScrapedMediaEntity)
    
    /**
     * 根据ID删除媒体
     */
    @Query("DELETE FROM scraped_media WHERE id = :id")
    suspend fun deleteMediaById(id: String)
    
    /**
     * 清空所有媒体
     */
    @Query("DELETE FROM scraped_media")
    suspend fun clearAllMedia()
    
    /**
     * 获取媒体总数
     */
    @Query("SELECT COUNT(*) FROM scraped_media")
    suspend fun getMediaCount(): Int
    
    /**
     * 获取电影总数
     */
    @Query("SELECT COUNT(*) FROM scraped_media WHERE mediaType = 'movie'")
    suspend fun getMovieCount(): Int
    
    /**
     * 获取电视剧总数
     */
    @Query("SELECT COUNT(*) FROM scraped_media WHERE mediaType = 'tv'")
    suspend fun getTvShowCount(): Int
}
