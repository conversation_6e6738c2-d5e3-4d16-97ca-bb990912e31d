/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 刮削媒体实体
 */
@Entity(tableName = "scraped_media")
data class ScrapedMediaEntity(
    @PrimaryKey
    val id: String,
    val tmdbId: Int,
    val title: String,
    val originalTitle: String,
    val overview: String,
    val posterPath: String?,
    val backdropPath: String?,
    val releaseDate: String?,
    val voteAverage: Double,
    val voteCount: Int,
    val popularity: Double,
    val originalLanguage: String,
    val genreIds: String, // JSON字符串存储类型ID列表
    val mediaType: String, // "movie" 或 "tv"
    val filePath: String, // 文件在WebDAV中的路径
    val fileName: String, // 原始文件名
    val fileSize: Long = 0L,
    val addedAt: Long = System.currentTimeMillis(),
    val lastScrapedAt: Long = System.currentTimeMillis(),
    
    // 电视剧特有字段
    val season: Int? = null,
    val episode: Int? = null,
    val firstAirDate: String? = null,
    
    // 电影特有字段
    val runtime: Int? = null,
    val budget: Long? = null,
    val revenue: Long? = null
)
