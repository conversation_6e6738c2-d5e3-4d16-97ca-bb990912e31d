/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.scraper

import java.util.regex.Pattern

/**
 * 文件名解析工具类
 */
object FileNameParser {
    
    // 视频文件扩展名
    private val videoExtensions = setOf(
        "mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts", "m2ts"
    )
    
    // 年份正则表达式
    private val yearPattern = Pattern.compile("\\b(19|20)\\d{2}\\b")
    
    // 季集信息正则表达式
    private val seasonEpisodePattern = Pattern.compile("S(\\d+)E(\\d+)", Pattern.CASE_INSENSITIVE)
    
    // 分辨率正则表达式
    private val resolutionPattern = Pattern.compile("\\b(720p|1080p|4K|2160p)\\b", Pattern.CASE_INSENSITIVE)
    
    // 需要移除的关键词
    private val removeKeywords = setOf(
        "BluRay", "BDRip", "DVDRip", "WEBRip", "HDTV", "x264", "x265", "HEVC", "AAC", "DTS",
        "AC3", "5.1", "7.1", "REMUX", "PROPER", "REPACK", "EXTENDED", "UNRATED", "DC",
        "IMAX", "HDR", "Dolby", "Vision", "Atmos", "CHINESE", "MANDARIN", "CANTONESE"
    )
    
    /**
     * 解析文件信息
     */
    fun parseFileName(fileName: String): ParsedFileInfo {
        val nameWithoutExtension = removeExtension(fileName)
        val cleanName = cleanFileName(nameWithoutExtension)
        val year = extractYear(nameWithoutExtension)
        val seasonEpisode = extractSeasonEpisode(nameWithoutExtension)
        val isMovie = seasonEpisode == null
        
        return ParsedFileInfo(
            originalFileName = fileName,
            cleanName = cleanName,
            year = year,
            season = seasonEpisode?.first,
            episode = seasonEpisode?.second,
            isMovie = isMovie,
            isVideoFile = isVideoFile(fileName)
        )
    }
    
    /**
     * 检查是否为视频文件
     */
    fun isVideoFile(fileName: String): Boolean {
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return extension in videoExtensions
    }
    
    /**
     * 移除文件扩展名
     */
    private fun removeExtension(fileName: String): String {
        return fileName.substringBeforeLast('.')
    }
    
    /**
     * 清理文件名
     */
    private fun cleanFileName(fileName: String): String {
        var cleaned = fileName
        
        // 移除年份
        cleaned = yearPattern.matcher(cleaned).replaceAll("")
        
        // 移除季集信息
        cleaned = seasonEpisodePattern.matcher(cleaned).replaceAll("")
        
        // 移除分辨率信息
        cleaned = resolutionPattern.matcher(cleaned).replaceAll("")
        
        // 移除关键词
        removeKeywords.forEach { keyword ->
            cleaned = cleaned.replace(keyword, "", ignoreCase = true)
        }
        
        // 替换分隔符为空格
        cleaned = cleaned.replace(Regex("[._\\-\\[\\](){}]"), " ")
        
        // 移除多余空格
        cleaned = cleaned.replace(Regex("\\s+"), " ").trim()
        
        return cleaned
    }
    
    /**
     * 提取年份
     */
    private fun extractYear(fileName: String): Int? {
        val matcher = yearPattern.matcher(fileName)
        return if (matcher.find()) {
            matcher.group().toIntOrNull()
        } else {
            null
        }
    }
    
    /**
     * 提取季集信息
     */
    private fun extractSeasonEpisode(fileName: String): Pair<Int, Int>? {
        val matcher = seasonEpisodePattern.matcher(fileName)
        return if (matcher.find()) {
            val season = matcher.group(1).toIntOrNull()
            val episode = matcher.group(2).toIntOrNull()
            if (season != null && episode != null) {
                Pair(season, episode)
            } else {
                null
            }
        } else {
            null
        }
    }
}

/**
 * 解析后的文件信息
 */
data class ParsedFileInfo(
    val originalFileName: String,
    val cleanName: String,
    val year: Int?,
    val season: Int?,
    val episode: Int?,
    val isMovie: Boolean,
    val isVideoFile: Boolean
)
