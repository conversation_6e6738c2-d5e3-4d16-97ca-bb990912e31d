/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.tmdb

/**
 * TMDB API配置
 */
object TmdbConfig {
    const val BASE_URL = "https://api.themoviedb.org/3/"
    const val IMAGE_BASE_URL = "https://image.tmdb.org/t/p/"
    const val API_KEY = "e5ea1ff22ac53933400bc0251fff5943"
    const val ACCESS_TOKEN = "eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************.GRMPv8PEpwrEm-6zCPmSr4uFH5JFkJPMQq9P44IvcvM"
    
    // 图片尺寸配置
    const val POSTER_SIZE_W342 = "w342"
    const val POSTER_SIZE_W500 = "w500"
    const val BACKDROP_SIZE_W780 = "w780"
    const val BACKDROP_SIZE_W1280 = "w1280"
    
    /**
     * 获取完整的图片URL
     */
    fun getImageUrl(imagePath: String?, size: String = POSTER_SIZE_W500): String {
        return if (imagePath != null) {
            "$IMAGE_BASE_URL$size$imagePath"
        } else {
            ""
        }
    }
    
    /**
     * 获取海报URL
     */
    fun getPosterUrl(posterPath: String?): String {
        return getImageUrl(posterPath, POSTER_SIZE_W500)
    }
    
    /**
     * 获取背景图URL
     */
    fun getBackdropUrl(backdropPath: String?): String {
        return getImageUrl(backdropPath, BACKDROP_SIZE_W780)
    }
}
