/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.data.repositories

import com.google.jetstream.data.database.dao.ScrapedMediaDao
import com.google.jetstream.data.database.entities.ScrapedMediaEntity
import com.google.jetstream.data.entities.Movie
import com.google.jetstream.data.tmdb.TmdbConfig
import com.google.jetstream.data.tmdb.models.TmdbMovie
import com.google.jetstream.data.tmdb.models.TmdbTvShow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 刮削媒体数据仓库
 */
@Singleton
class ScrapedMediaRepository @Inject constructor(
    private val scrapedMediaDao: ScrapedMediaDao
) {
    
    /**
     * 获取所有刮削的电影
     */
    fun getAllMovies(): Flow<List<Movie>> {
        return scrapedMediaDao.getAllMovies().map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 获取所有刮削的电视剧
     */
    fun getAllTvShows(): Flow<List<Movie>> {
        return scrapedMediaDao.getAllTvShows().map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 获取最近添加的电影
     */
    fun getRecentMovies(limit: Int = 10): Flow<List<Movie>> {
        return scrapedMediaDao.getRecentMovies(limit).map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 获取最近添加的电视剧
     */
    fun getRecentTvShows(limit: Int = 10): Flow<List<Movie>> {
        return scrapedMediaDao.getRecentTvShows(limit).map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 获取热门电影
     */
    fun getTopRatedMovies(limit: Int = 10): Flow<List<Movie>> {
        return scrapedMediaDao.getTopRatedMovies(limit).map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 获取热门电视剧
     */
    fun getTopRatedTvShows(limit: Int = 10): Flow<List<Movie>> {
        return scrapedMediaDao.getTopRatedTvShows(limit).map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 搜索媒体
     */
    fun searchMedia(query: String): Flow<List<Movie>> {
        return scrapedMediaDao.searchMedia(query).map { entities ->
            entities.map { it.toMovie() }
        }
    }
    
    /**
     * 保存TMDB电影到数据库
     */
    suspend fun saveTmdbMovie(tmdbMovie: TmdbMovie, filePath: String, fileName: String) {
        val entity = ScrapedMediaEntity(
            id = UUID.randomUUID().toString(),
            tmdbId = tmdbMovie.id,
            title = tmdbMovie.title,
            originalTitle = tmdbMovie.originalTitle,
            overview = tmdbMovie.overview ?: "",
            posterPath = tmdbMovie.posterPath,
            backdropPath = tmdbMovie.backdropPath,
            releaseDate = tmdbMovie.releaseDate,
            voteAverage = tmdbMovie.voteAverage,
            voteCount = tmdbMovie.voteCount,
            popularity = tmdbMovie.popularity,
            originalLanguage = tmdbMovie.originalLanguage,
            genreIds = tmdbMovie.genreIds.joinToString(","),
            mediaType = "movie",
            filePath = filePath,
            fileName = fileName
        )
        
        // 检查是否已存在相同的TMDB电影
        val existing = scrapedMediaDao.getMediaByTmdbId(tmdbMovie.id, "movie")
        if (existing == null) {
            scrapedMediaDao.insertMedia(entity)
        }
    }
    
    /**
     * 保存TMDB电视剧到数据库
     */
    suspend fun saveTmdbTvShow(tmdbTvShow: TmdbTvShow, filePath: String, fileName: String) {
        val entity = ScrapedMediaEntity(
            id = UUID.randomUUID().toString(),
            tmdbId = tmdbTvShow.id,
            title = tmdbTvShow.name,
            originalTitle = tmdbTvShow.originalName,
            overview = tmdbTvShow.overview ?: "",
            posterPath = tmdbTvShow.posterPath,
            backdropPath = tmdbTvShow.backdropPath,
            releaseDate = null,
            voteAverage = tmdbTvShow.voteAverage,
            voteCount = tmdbTvShow.voteCount,
            popularity = tmdbTvShow.popularity,
            originalLanguage = tmdbTvShow.originalLanguage,
            genreIds = tmdbTvShow.genreIds.joinToString(","),
            mediaType = "tv",
            filePath = filePath,
            fileName = fileName,
            firstAirDate = tmdbTvShow.firstAirDate
        )
        
        // 检查是否已存在相同的TMDB电视剧
        val existing = scrapedMediaDao.getMediaByTmdbId(tmdbTvShow.id, "tv")
        if (existing == null) {
            scrapedMediaDao.insertMedia(entity)
        }
    }
    
    /**
     * 根据文件路径检查媒体是否已存在
     */
    suspend fun isMediaExistsByFilePath(filePath: String): Boolean {
        return scrapedMediaDao.getMediaByFilePath(filePath) != null
    }
    
    /**
     * 清空所有刮削的媒体
     */
    suspend fun clearAllMedia() {
        scrapedMediaDao.clearAllMedia()
    }
    
    /**
     * 获取媒体统计信息
     */
    suspend fun getMediaStats(): MediaStats {
        return MediaStats(
            totalCount = scrapedMediaDao.getMediaCount(),
            movieCount = scrapedMediaDao.getMovieCount(),
            tvShowCount = scrapedMediaDao.getTvShowCount()
        )
    }
    
    /**
     * 将ScrapedMediaEntity转换为Movie
     */
    private fun ScrapedMediaEntity.toMovie(): Movie {
        return Movie(
            id = id,
            videoUri = filePath,
            subtitleUri = null,
            posterUri = TmdbConfig.getPosterUrl(posterPath),
            name = title,
            description = overview
        )
    }
}

/**
 * 媒体统计信息
 */
data class MediaStats(
    val totalCount: Int,
    val movieCount: Int,
    val tvShowCount: Int
)
